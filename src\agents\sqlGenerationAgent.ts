import openai from '@/lib/openai';
import { SQLGenerationRequest, SQLGenerationResult, Agent } from '@/types';
import { prepareSpecificationForAPI } from '@/lib/textUtils';

export const sqlGenerationAgent: Agent = {
  id: 'sql-generation-agent',
  name: 'Agent de Génération SQL',
  description: 'Génère des scripts SQL à partir de spécifications fonctionnelles',
  role: 'Expert en bases de données et génération de scripts SQL',
  goal: 'Créer des scripts SQL optimisés et bien structurés à partir de spécifications',
  prompt: `Tu es un expert en bases de données et génération de scripts SQL.
  Ton rôle est de transformer des spécifications fonctionnelles en scripts SQL optimisés.
  
  Tu dois :
  1. Analyser les spécifications fournies
  2. Identifier les entités, relations et contraintes
  3. Générer un script SQL complet et optimisé
  4. Inclure des commentaires explicatifs
  5. Respecter les bonnes pratiques SQL
  6. Proposer des index appropriés si nécessaire
  
  Tu dois fournir un script SQL professionnel avec des explications détaillées.`,
  tools: ['sql-generation', 'schema-analysis', 'optimization'],
  utils: ['formatDate', 'validateSQL']
};

export class SQLGenerationService {
  private agent: Agent;

  constructor() {
    this.agent = sqlGenerationAgent;
  }

  async generateSQL(request: SQLGenerationRequest): Promise<SQLGenerationResult> {
    try {
      const { specification, databaseType = 'mysql', includeComments = true, isFromOfficeFile = false } = request;

      // Préparer la spécification en gérant la longueur et le type de fichier
      const preparedSpec = prepareSpecificationForAPI(specification, isFromOfficeFile);

      // Prompt optimisé et plus concis
      const prompt = `Tu es un expert SQL. Génère un script ${databaseType.toUpperCase()} basé sur cette spécification.

SPÉCIFICATION:
${preparedSpec.processedText}

${preparedSpec.wasTruncated ? '⚠️ Note: La spécification a été résumée automatiquement pour respecter les limites de tokens.' : ''}

Réponds en JSON:
{
  "sql": "Script SQL complet",
  "explanation": "Explication concise",
  "tables": ["table1", "table2"],
  "operations": ["CREATE", "INSERT"]
}

Le script SQL doit être optimisé pour ${databaseType} et inclure :
- Création des tables avec contraintes appropriées
- Index recommandés
- Commentaires explicatifs ${includeComments ? '' : '(optionnels)'}
- Données d'exemple si pertinent
- Procédures stockées si nécessaire`;

      const response = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: this.agent.prompt
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.2,
        max_tokens: 3000
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('Aucune réponse reçue de l\'API OpenAI');
      }

      // Try to parse JSON response
      let sqlResult: SQLGenerationResult;
      try {
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          sqlResult = JSON.parse(jsonMatch[0]);
        } else {
          // Fallback if no JSON found
          sqlResult = {
            sql: content,
            explanation: 'Script SQL généré à partir de la spécification',
            tables: [],
            operations: [],
            timestamp: new Date().toISOString()
          };
        }
      } catch (parseError) {
        // Fallback parsing - extract SQL from content
        const sqlMatch = content.match(/```sql\n([\s\S]*?)\n```/) || content.match(/```\n([\s\S]*?)\n```/);
        const sql = sqlMatch ? sqlMatch[1] : content;
        
        sqlResult = {
          sql: sql,
          explanation: content,
          tables: this.extractTables(sql),
          operations: this.extractOperations(sql),
          timestamp: new Date().toISOString()
        };
      }

      sqlResult.timestamp = new Date().toISOString();
      return sqlResult;

    } catch (error) {
      console.error('Erreur lors de la génération SQL:', error);
      throw new Error(`Erreur lors de la génération du script SQL: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
  }

  private extractTables(sql: string): string[] {
    const tableMatches = sql.match(/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?`?(\w+)`?/gi);
    return tableMatches ? tableMatches.map(match => {
      const tableMatch = match.match(/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?`?(\w+)`?/i);
      return tableMatch ? tableMatch[1] : '';
    }).filter(Boolean) : [];
  }

  private extractOperations(sql: string): string[] {
    const operations = new Set<string>();
    const operationMatches = sql.match(/\b(CREATE|INSERT|UPDATE|DELETE|SELECT|ALTER|DROP|INDEX)\b/gi);
    if (operationMatches) {
      operationMatches.forEach(op => operations.add(op.toUpperCase()));
    }
    return Array.from(operations);
  }

  getAgentInfo(): Agent {
    return this.agent;
  }
}
