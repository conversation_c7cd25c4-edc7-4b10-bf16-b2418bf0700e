import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('fr-FR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date)
}

export interface ParsedError {
  timestamp: string;
  level: string;
  message: string;
  task?: string;
  lineNumber?: number;
  errorType?: string;
  severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  context?: string;
  fileName?: string;
}

export function parseErrorFile(content: string): ParsedError[] {
  const lines = content.split('\n').filter(line => line.trim());
  return lines.map((line, index) => {
    // Pattern principal pour les logs CAOFORS
    const caoMatch = line.match(/^(\d{2}\.\d{2}\.\d{4} \d{2}:\d{2}:\d{2})\s+(\w+)\s+(.+)$/);

    if (caoMatch) {
      const [, timestamp, level, message] = caoMatch;
      const taskMatch = message.match(/task \[([^\]]+)\]/);

      // Patterns étendus pour extraire les numéros de ligne
      const linePatterns = [
        /(?:line|ligne)[:\s]+(\d+)/i,                    // line: 123, ligne: 123
        /\[.*(?:line|ligne)[:\s]+(\d+)/i,                // [something line: 123]
        /caofors\.ec line: (\d+)/,                       // caofors.ec line: 123
        /at line (\d+)/i,                                // at line 123
        /error on line (\d+)/i,                          // error on line 123
        /(\d+):\d+:/,                                    // 123:45: (format file:line:col)
        /line (\d+) column \d+/i,                        // line 123 column 45
        /\((\d+),\d+\)/,                                 // (123,45) format
        /:\s*(\d+)\s*:/,                                 // : 123 :
        /ligne\s+(\d+)/i,                                // ligne 123
        /row\s+(\d+)/i,                                  // row 123
        /position\s+(\d+)/i                              // position 123
      ];

      let lineNumber: number | undefined;
      for (const pattern of linePatterns) {
        const match = message.match(pattern);
        if (match) {
          lineNumber = parseInt(match[1]);
          break;
        }
      }

      // Extraction du nom de fichier
      const filePatterns = [
        /([a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z]+)/,          // filename.ext
        /in file ([^\s]+)/i,                             // in file filename
        /file "([^"]+)"/i,                               // file "filename"
        /([^\s]+\.(?:c|cpp|h|hpp|ec|sql|js|ts|py|java|cs))/i // common extensions
      ];

      let fileName: string | undefined;
      for (const pattern of filePatterns) {
        const match = message.match(pattern);
        if (match) {
          fileName = match[1];
          break;
        }
      }

      // Détection du type d'erreur
      const errorType = detectErrorType(message);

      // Détection de la sévérité
      const severity = detectSeverity(level, message);

      return {
        timestamp,
        level,
        message,
        task: taskMatch ? taskMatch[1] : undefined,
        lineNumber,
        errorType,
        severity,
        fileName
      };
    }

    // Patterns pour d'autres formats de logs
    const genericPatterns = [
      // Format standard: ERROR: message at line 123
      /^(ERROR|WARNING|INFO|DEBUG):\s*(.+?)(?:\s+at\s+line\s+(\d+))?$/i,
      // Format avec timestamp ISO
      /^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})\s+(\w+)\s+(.+)$/,
      // Format simple: level message
      /^(\w+):\s*(.+)$/
    ];

    for (const pattern of genericPatterns) {
      const match = line.match(pattern);
      if (match) {
        const level = match[1] || match[2] || 'UNKNOWN';
        const message = match[2] || match[3] || match[1] || line;
        const lineNumber = match[3] ? parseInt(match[3]) : undefined;

        return {
          timestamp: match[1]?.includes('T') ? match[1] : '',
          level,
          message,
          lineNumber,
          errorType: detectErrorType(message),
          severity: detectSeverity(level, message)
        };
      }
    }

    // Fallback pour les lignes non reconnues
    return {
      timestamp: '',
      level: 'UNKNOWN',
      message: line,
      lineNumber: index + 1,
      errorType: 'UNKNOWN',
      severity: 'LOW'
    };
  });
}

function detectErrorType(message: string): string {
  const errorPatterns = [
    { pattern: /variable.*not.*(?:initialized|declared|defined)/i, type: 'VARIABLE_NOT_INITIALIZED' },
    { pattern: /undefined.*variable/i, type: 'UNDEFINED_VARIABLE' },
    { pattern: /syntax.*error/i, type: 'SYNTAX_ERROR' },
    { pattern: /compilation.*error/i, type: 'COMPILATION_ERROR' },
    { pattern: /runtime.*error/i, type: 'RUNTIME_ERROR' },
    { pattern: /null.*pointer/i, type: 'NULL_POINTER' },
    { pattern: /memory.*leak/i, type: 'MEMORY_LEAK' },
    { pattern: /buffer.*overflow/i, type: 'BUFFER_OVERFLOW' },
    { pattern: /division.*by.*zero/i, type: 'DIVISION_BY_ZERO' },
    { pattern: /file.*not.*found/i, type: 'FILE_NOT_FOUND' },
    { pattern: /permission.*denied/i, type: 'PERMISSION_DENIED' },
    { pattern: /connection.*failed/i, type: 'CONNECTION_ERROR' },
    { pattern: /timeout/i, type: 'TIMEOUT_ERROR' },
    { pattern: /locked.*already.*runs/i, type: 'RESOURCE_LOCKED' },
    { pattern: /sql.*error/i, type: 'SQL_ERROR' },
    { pattern: /database.*error/i, type: 'DATABASE_ERROR' },
    { pattern: /assertion.*failed/i, type: 'ASSERTION_FAILED' },
    { pattern: /stack.*overflow/i, type: 'STACK_OVERFLOW' },
    { pattern: /out.*of.*memory/i, type: 'OUT_OF_MEMORY' },
    { pattern: /invalid.*argument/i, type: 'INVALID_ARGUMENT' },
    { pattern: /type.*mismatch/i, type: 'TYPE_MISMATCH' }
  ];

  for (const { pattern, type } of errorPatterns) {
    if (pattern.test(message)) {
      return type;
    }
  }

  return 'GENERAL_ERROR';
}

function detectSeverity(level: string, message: string): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
  // Sévérité basée sur le niveau
  const levelSeverity: Record<string, 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'> = {
    'DEBUG': 'LOW',
    'INFO': 'LOW',
    'WARNING': 'MEDIUM',
    'WARN': 'MEDIUM',
    'ERROR': 'HIGH',
    'FATAL': 'CRITICAL',
    'CRITICAL': 'CRITICAL'
  };

  let severity = levelSeverity[level.toUpperCase()] || 'MEDIUM';

  // Ajustement basé sur le contenu du message
  const criticalPatterns = [
    /crash/i, /fatal/i, /critical/i, /system.*failure/i,
    /memory.*corruption/i, /security.*breach/i
  ];

  const highPatterns = [
    /error/i, /exception/i, /failed/i, /abort/i,
    /null.*pointer/i, /buffer.*overflow/i, /stack.*overflow/i
  ];

  const lowPatterns = [
    /warning/i, /info/i, /debug/i, /notice/i
  ];

  if (criticalPatterns.some(pattern => pattern.test(message))) {
    severity = 'CRITICAL';
  } else if (highPatterns.some(pattern => pattern.test(message))) {
    severity = severity === 'LOW' ? 'HIGH' : severity;
  } else if (lowPatterns.some(pattern => pattern.test(message))) {
    severity = severity === 'HIGH' ? 'MEDIUM' : severity;
  }

  return severity;
}

export interface CodeContext {
  targetLine: string;
  contextLines: Array<{ number: number; content: string; isTarget: boolean }>;
  analysis?: {
    variables: string[];
    functions: string[];
    potentialIssues: string[];
    suggestions: string[];
  };
}

export function extractLineFromProgram(programContent: string, lineNumber: number, context: number = 2): CodeContext {
  const lines = programContent.split('\n');
  const targetLine = lines[lineNumber - 1] || '';

  const startLine = Math.max(0, lineNumber - context - 1);
  const endLine = Math.min(lines.length, lineNumber + context);

  const contextLines = [];
  for (let i = startLine; i < endLine; i++) {
    contextLines.push({
      number: i + 1,
      content: lines[i] || '',
      isTarget: i === lineNumber - 1
    });
  }

  // Analyse contextuelle avancée
  const analysis = analyzeCodeContext(lines, lineNumber, context);

  return {
    targetLine,
    contextLines,
    analysis
  };
}

export function analyzeCodeContext(lines: string[], lineNumber: number, context: number = 5): {
  variables: string[];
  functions: string[];
  potentialIssues: string[];
  suggestions: string[];
} {
  const startLine = Math.max(0, lineNumber - context - 1);
  const endLine = Math.min(lines.length, lineNumber + context);
  const contextCode = lines.slice(startLine, endLine).join('\n');
  const targetLine = lines[lineNumber - 1] || '';

  // Extraction des variables
  const variables = extractVariables(contextCode);

  // Extraction des fonctions
  const functions = extractFunctions(contextCode);

  // Détection des problèmes potentiels
  const potentialIssues = detectPotentialIssues(targetLine, contextCode);

  // Génération de suggestions
  const suggestions = generateSuggestions(targetLine, contextCode, potentialIssues);

  return {
    variables,
    functions,
    potentialIssues,
    suggestions
  };
}

function extractVariables(code: string): string[] {
  const variables = new Set<string>();

  // Patterns pour différents langages
  const patterns = [
    // C/C++: type var = value; ou type var;
    /(?:int|float|double|char|long|short|unsigned|signed|bool|void)\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,
    // Variables avec déclaration explicite
    /(?:var|let|const)\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,
    // Assignations
    /([a-zA-Z_][a-zA-Z0-9_]*)\s*=/g,
    // Paramètres de fonction
    /\(\s*([a-zA-Z_][a-zA-Z0-9_]*(?:\s*,\s*[a-zA-Z_][a-zA-Z0-9_]*)*)\s*\)/g
  ];

  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(code)) !== null) {
      if (match[1]) {
        // Séparer les variables multiples (pour les paramètres)
        match[1].split(',').forEach(v => {
          const varName = v.trim().split(/\s+/).pop();
          if (varName && varName.length > 1) {
            variables.add(varName);
          }
        });
      }
    }
  });

  return Array.from(variables);
}

function extractFunctions(code: string): string[] {
  const functions = new Set<string>();

  const patterns = [
    // Définitions de fonction C/C++
    /(?:int|float|double|char|long|short|unsigned|signed|bool|void)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,
    // Appels de fonction
    /([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,
    // Fonctions JavaScript/TypeScript
    /function\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,
    // Méthodes
    /\.([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g
  ];

  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(code)) !== null) {
      if (match[1] && match[1].length > 1) {
        functions.add(match[1]);
      }
    }
  });

  return Array.from(functions);
}

function detectPotentialIssues(targetLine: string, contextCode: string): string[] {
  const issues: string[] = [];

  // Vérifications communes
  const checks = [
    {
      pattern: /([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*[^;]*$/,
      message: "Variable potentiellement non initialisée ou assignation incomplète"
    },
    {
      pattern: /\*\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*(?![=])/,
      message: "Déréférencement de pointeur - vérifier si le pointeur est NULL"
    },
    {
      pattern: /\[\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\]/,
      message: "Accès tableau - vérifier les limites d'index"
    },
    {
      pattern: /\/\*.*\*\//,
      message: "Code commenté - peut indiquer du code problématique"
    },
    {
      pattern: /TODO|FIXME|HACK|BUG/i,
      message: "Commentaire indiquant un problème connu"
    },
    {
      pattern: /malloc|calloc|free/,
      message: "Gestion mémoire manuelle - vérifier les fuites mémoire"
    },
    {
      pattern: /strcpy|strcat|sprintf/,
      message: "Fonction potentiellement dangereuse - risque de buffer overflow"
    }
  ];

  checks.forEach(check => {
    if (check.pattern.test(targetLine)) {
      issues.push(check.message);
    }
  });

  // Vérifications contextuelles
  if (contextCode.includes('EXEC SQL') && !contextCode.includes('SQLCODE_ERROR')) {
    issues.push("Requête SQL sans vérification d'erreur appropriée");
  }

  if (targetLine.includes('=') && !targetLine.includes(';')) {
    issues.push("Assignation sans point-virgule terminal");
  }

  return issues;
}

function generateSuggestions(targetLine: string, contextCode: string, issues: string[]): string[] {
  const suggestions: string[] = [];

  // Suggestions basées sur les problèmes détectés
  if (issues.some(issue => issue.includes('non initialisée'))) {
    suggestions.push("Initialiser la variable avant utilisation");
    suggestions.push("Vérifier la déclaration de la variable");
  }

  if (issues.some(issue => issue.includes('pointeur'))) {
    suggestions.push("Ajouter une vérification NULL avant déréférencement");
    suggestions.push("Utiliser des pointeurs intelligents si possible");
  }

  if (issues.some(issue => issue.includes('tableau'))) {
    suggestions.push("Vérifier que l'index est dans les limites du tableau");
    suggestions.push("Utiliser des fonctions sécurisées pour l'accès aux tableaux");
  }

  if (targetLine.includes('EXEC SQL')) {
    suggestions.push("Ajouter SQLCODE_ERROR_PUTERRDB_RET après la requête SQL");
    suggestions.push("Vérifier le code de retour SQL");
  }

  // Suggestions générales
  if (targetLine.trim().length === 0) {
    suggestions.push("Ligne vide - vérifier si du code manque");
  }

  if (targetLine.includes('//') || targetLine.includes('/*')) {
    suggestions.push("Code commenté - vérifier si c'est intentionnel");
  }

  return suggestions;
}
