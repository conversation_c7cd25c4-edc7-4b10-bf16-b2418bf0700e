'use client'

import { Sparkles } from 'lucide-react'

export default function Loading() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="text-center">
        <div className="relative">
          <div className="w-16 h-16 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse">
            <Sparkles className="w-8 h-8 text-white animate-spin" />
          </div>
          <div className="absolute inset-0 w-16 h-16 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-full mx-auto animate-ping opacity-20"></div>
        </div>
        
        <h2 className="text-2xl font-bold text-[#002857] mb-2">
          Leoni Agents
        </h2>
        
        <p className="text-gray-600 mb-4">
          Chargement en cours...
        </p>
        
        <div className="flex justify-center">
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-[#ff7514] rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-[#ff7514] rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-[#ff7514] rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>
    </div>
  )
}
