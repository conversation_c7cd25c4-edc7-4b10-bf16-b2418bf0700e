Test de spécification pour base de données e-commerce

Tables principales :
- Utilisateurs (id, nom, email, mot_de_passe, date_creation)
- Produits (id, nom, description, prix, stock, categorie_id)
- Categories (id, nom, description)
- Commandes (id, utilisateur_id, date_commande, total, statut)
- Details_commande (id, commande_id, produit_id, quantite, prix_unitaire)

Contraintes :
- Clés primaires sur tous les id
- Clés étrangères appropriées
- Index sur email des utilisateurs
- Index sur nom des produits
- Contraintes de validation sur les prix (> 0)
- Contraintes de validation sur les quantités (> 0)

Données d'exemple souhaitées :
- 3 catégories : Électronique, Vêtements, Livres
- 10 produits répartis dans les catégories
- 5 utilisateurs de test
- Quelques commandes d'exemple
