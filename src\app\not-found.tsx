'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Home, Search, ArrowLeft } from 'lucide-react'

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="max-w-lg w-full mx-4">
        <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
          <div className="w-20 h-20 bg-gradient-to-r from-[#002857] to-[#003d7a] rounded-full flex items-center justify-center mx-auto mb-6">
            <Search className="w-10 h-10 text-white" />
          </div>
          
          <h1 className="text-6xl font-bold text-[#002857] mb-4">
            404
          </h1>
          
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Page non trouvée
          </h2>
          
          <p className="text-gray-600 mb-8">
            D<PERSON><PERSON><PERSON>, la page que vous recherchez n&apos;existe pas ou a été déplacée.
          </p>
          
          <div className="space-y-3">
            <Link href="/">
              <Button className="w-full bg-gradient-to-r from-[#ff7514] to-[#ff8c42] hover:from-[#e6650f] hover:to-[#ff7514] text-white">
                <Home className="w-4 h-4 mr-2" />
                Retour à l&apos;accueil
              </Button>
            </Link>
            
            <Button 
              variant="outline" 
              onClick={() => window.history.back()}
              className="w-full border-[#002857] text-[#002857] hover:bg-[#002857] hover:text-white"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Page précédente
            </Button>
          </div>
          
          <div className="mt-8 pt-6 border-t border-gray-200">
            <p className="text-sm text-gray-500">
              Besoin d&apos;aide ? Contactez notre équipe support.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
