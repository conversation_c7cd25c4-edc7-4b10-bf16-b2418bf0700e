SPÉCIFICATION SYSTÈME DE GESTION DE BIBLIOTHÈQUE

Tables requises :

1. AUTEURS
   - id_auteur (clé primaire, auto-incrémentée)
   - nom (varchar 100, non null)
   - prenom (varchar 100, non null)
   - date_naissance (date)
   - nationalite (varchar 50)

2. LIVRES
   - id_livre (clé primaire, auto-incrémentée)
   - titre (varchar 200, non null)
   - isbn (varchar 20, unique)
   - date_publication (date)
   - nombre_pages (integer)
   - id_auteur (clé étrangère vers AUTEURS)

3. MEMBRES
   - id_membre (clé primaire, auto-incrémentée)
   - nom (varchar 100, non null)
   - prenom (varchar 100, non null)
   - email (varchar 150, unique, non null)
   - telephone (varchar 20)
   - date_inscription (date, non null)
   - statut (enum: 'actif', 'suspendu', 'inactif')

4. EMPRUNTS
   - id_emprunt (clé primaire, auto-incrémentée)
   - id_livre (clé étrangère vers LIVRES)
   - id_membre (clé étrangère vers MEMBRES)
   - date_emprunt (date, non null)
   - date_retour_prevue (date, non null)
   - date_retour_effective (date, nullable)
   - statut (enum: 'en_cours', 'retourne', 'en_retard')

Contraintes :
- Un membre ne peut emprunter plus de 5 livres simultanément
- La durée d'emprunt standard est de 14 jours
- Index sur les colonnes fréquemment recherchées (email, isbn, dates)
- Contraintes de validation sur les dates (date_retour_prevue > date_emprunt)

Données d'exemple à insérer :
- 3 auteurs
- 5 livres
- 2 membres
- 2 emprunts en cours
