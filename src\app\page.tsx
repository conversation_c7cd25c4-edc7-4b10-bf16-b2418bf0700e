import Link from "next/link";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Bug, Database, Sparkles, ArrowRight, Brain, C<PERSON>, Rocket } from "lucide-react";

export default function Home() {
  return (
    <div className="relative">
      {/* Hero Section avec animations */}
      <div className="relative overflow-hidden">
        <div className="container mx-auto px-4 py-20">
          <div className="text-center mb-16">
            {/* Badge animé avec couleurs corporate */}
            <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#002857]/10 to-[#ff7514]/10 rounded-full mb-8 animate-bounce border border-[#ff7514]/20">
              <Sparkles className="w-5 h-5 text-[#ff7514] mr-2" />
              <span className="text-sm font-semibold text-[#002857]">Nouvelle Génération d&apos;IA</span>
              <Rocket className="w-5 h-5 text-[#ff7514] ml-2" />
            </div>

            {/* Titre principal avec gradient */}
            <h1 className="text-6xl md:text-7xl font-black mb-6 leading-tight">
              <span className="bg-gradient-to-r from-[#002857] via-[#003d7a] to-[#ff7514] bg-clip-text text-transparent">
                Leoni Agents
              </span>
              <br />
              <span className="text-[#002857] text-4xl md:text-5xl font-bold">
                Intelligence Artificielle
              </span>
            </h1>

            {/* Sous-titre élégant */}
            <p className="text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed">
              Révolutionnez votre workflow avec nos agents IA de nouvelle génération.
              <span className="text-[#ff7514] font-semibold"> Analyse d&apos;erreurs intelligente</span> et
              <span className="text-[#002857] font-semibold"> génération SQL automatisée</span>
              pour une productivité sans limites.
            </p>

            {/* Boutons CTA avec effets */}
            <div className="flex flex-col sm:flex-row justify-center gap-6 mb-16">
              <Link href="/error-analysis">
                <Button size="lg" className="group relative bg-gradient-to-r from-[#ff7514] to-[#ff8c42] hover:from-[#e6650f] hover:to-[#ff7514] text-white px-8 py-4 rounded-2xl shadow-xl shadow-[#ff7514]/25 transform hover:scale-105 transition-all duration-300">
                  <Bug className="w-6 h-6 mr-3 group-hover:animate-pulse" />
                  Analyser les Erreurs
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
              <Link href="/sql-generation">
                <Button size="lg" variant="outline" className="group relative bg-white/90 backdrop-blur-sm border-2 border-[#002857]/30 hover:border-[#002857] text-[#002857] hover:text-[#001a3d] px-8 py-4 rounded-2xl shadow-xl hover:shadow-[#002857]/25 transform hover:scale-105 transition-all duration-300">
                  <Database className="w-6 h-6 mr-3 group-hover:animate-pulse" />
                  Générer du SQL
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </div>

            {/* Stats impressionnantes */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-[#ff7514] mb-2">99.9%</div>
                <div className="text-gray-600">Précision d&apos;analyse</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-[#002857] mb-2">&lt;2s</div>
                <div className="text-gray-600">Temps de réponse</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-[#ff8c42] mb-2">24/7</div>
                <div className="text-gray-600">Disponibilité</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Grid avec design moderne */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-[#002857] mb-4">
            Agents <span className="text-[#ff7514]">Spécialisés</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Découvrez nos agents IA de pointe, conçus pour transformer votre façon de travailler
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {/* Agent d'Analyse d'Erreurs */}
          <Card className="group relative overflow-hidden bg-gradient-to-br from-[#ff7514]/5 to-[#ff7514]/10 border-0 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105">
            <div className="absolute inset-0 bg-gradient-to-br from-[#ff7514]/10 to-[#ff8c42]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <CardHeader className="relative z-10">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-2xl shadow-lg shadow-[#ff7514]/30">
                  <Bug className="w-8 h-8 text-white" />
                </div>
                <div className="px-3 py-1 bg-[#ff7514]/10 text-[#e6650f] rounded-full text-xs font-semibold">
                  EXPERT
                </div>
              </div>
              <CardTitle className="text-2xl font-bold text-[#002857] mb-2">
                Agent d&apos;Analyse d&apos;Erreurs
              </CardTitle>
              <CardDescription className="text-gray-600 text-base">
                IA avancée pour la détection et résolution intelligente des erreurs de programme
              </CardDescription>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-gray-700">
                  <div className="w-2 h-2 bg-[#ff7514] rounded-full mr-3"></div>
                  <span>Analyse automatique des fichiers d&apos;erreur</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <div className="w-2 h-2 bg-[#ff7514] rounded-full mr-3"></div>
                  <span>Localisation précise avec numéros de ligne</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <div className="w-2 h-2 bg-[#ff7514] rounded-full mr-3"></div>
                  <span>Solutions détaillées et contexte de code</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <div className="w-2 h-2 bg-[#ff7514] rounded-full mr-3"></div>
                  <span>Support multi-langages de programmation</span>
                </div>
              </div>
              <Link href="/error-analysis">
                <Button className="w-full bg-gradient-to-r from-[#ff7514] to-[#ff8c42] hover:from-[#e6650f] hover:to-[#ff7514] text-white py-3 rounded-xl shadow-lg group-hover:shadow-[#ff7514]/25 transition-all duration-300">
                  <Brain className="w-5 h-5 mr-2" />
                  Commencer l&apos;Analyse
                  <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Agent de Génération SQL */}
          <Card className="group relative overflow-hidden bg-gradient-to-br from-[#002857]/5 to-[#002857]/10 border-0 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105">
            <div className="absolute inset-0 bg-gradient-to-br from-[#002857]/10 to-[#003d7a]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <CardHeader className="relative z-10">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-gradient-to-r from-[#002857] to-[#003d7a] rounded-2xl shadow-lg shadow-[#002857]/30">
                  <Database className="w-8 h-8 text-white" />
                </div>
                <div className="px-3 py-1 bg-[#002857]/10 text-[#001a3d] rounded-full text-xs font-semibold">
                  INNOVANT
                </div>
              </div>
              <CardTitle className="text-2xl font-bold text-[#002857] mb-2">
                Agent de Génération SQL
              </CardTitle>
              <CardDescription className="text-gray-600 text-base">
                Création automatique de scripts SQL optimisés à partir de spécifications
              </CardDescription>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-gray-700">
                  <div className="w-2 h-2 bg-[#002857] rounded-full mr-3"></div>
                  <span>Génération automatique de scripts SQL</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <div className="w-2 h-2 bg-[#002857] rounded-full mr-3"></div>
                  <span>Support multi-bases de données</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <div className="w-2 h-2 bg-[#002857] rounded-full mr-3"></div>
                  <span>Upload de fichiers de spécification</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <div className="w-2 h-2 bg-[#002857] rounded-full mr-3"></div>
                  <span>Optimisation et bonnes pratiques</span>
                </div>
              </div>
              <Link href="/sql-generation">
                <Button className="w-full bg-gradient-to-r from-[#002857] to-[#003d7a] hover:from-[#001a3d] hover:to-[#002857] text-white py-3 rounded-xl shadow-lg group-hover:shadow-[#002857]/25 transition-all duration-300">
                  <Cpu className="w-5 h-5 mr-2" />
                  Générer du SQL
                  <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
