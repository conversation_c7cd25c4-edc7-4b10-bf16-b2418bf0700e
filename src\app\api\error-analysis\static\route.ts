import { NextRequest, NextResponse } from 'next/server';
import { ErrorAnalysisService } from '@/agents/errorAnalysisAgent';
import { ProgramFile } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { programFile }: { programFile: ProgramFile } = body;

    if (!programFile) {
      return NextResponse.json(
        { error: 'Le fichier programme est requis' },
        { status: 400 }
      );
    }

    if (!programFile.content) {
      return NextResponse.json(
        { error: 'Le contenu du fichier programme ne peut pas être vide' },
        { status: 400 }
      );
    }

    const errorAnalysisService = new ErrorAnalysisService();
    const result = await errorAnalysisService.analyzeCodeOnly(programFile);

    return NextResponse.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erreur lors de l\'analyse statique:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur interne du serveur',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Endpoint d\'analyse statique disponible',
    description: 'Analyse un fichier de programme sans fichier d\'erreur pour détecter des problèmes potentiels',
    timestamp: new Date().toISOString()
  });
}
