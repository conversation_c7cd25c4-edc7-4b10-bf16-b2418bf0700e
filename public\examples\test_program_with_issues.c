#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Programme d'exemple avec plusieurs types d'erreurs pour tester l'agent amélioré

int main() {
    // Variable non initialisée
    int uninitialized_var;
    
    // Allocation mémoire sans libération
    char* buffer = malloc(100);
    
    // Fonction dangereuse
    char dest[10];
    strcpy(dest, "This string is too long for the destination buffer");
    
    // Utilisation de variable non initialisée
    printf("Valeur non initialisée: %d\n", uninitialized_var);
    
    // Code commenté problématique
    /* TODO: Fix this memory leak
    char* another_buffer = malloc(200);
    */
    
    // Requête SQL sans vérification d'erreur (simulation)
    EXEC SQL SELECT * FROM users WHERE id = :user_id;
    // Manque: SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "test.c", "users select");
    
    // Déréférencement de pointeur sans vérification NULL
    char* ptr = NULL;
    printf("Contenu: %s\n", *ptr);
    
    // Accès tableau sans vérification des limites
    int array[5];
    int index = 10;
    array[index] = 42;
    
    // Complexité excessive sur une ligne
    if (uninitialized_var > 0 && buffer != NULL && index < 5 && ptr != NULL && dest[0] != '\0') {
        printf("Condition complexe\n");
    }
    
    // Fonction sprintf dangereuse
    char output[50];
    sprintf(output, "User input: %s and number: %d", dest, uninitialized_var);
    
    return 0;
    // Note: buffer n'est jamais libéré - fuite mémoire
}

// Fonction avec variable non initialisée
int calculate_something() {
    int result;
    int factor = 10;
    
    // result n'est pas initialisé avant utilisation
    return result * factor;
}

// Fonction avec gestion mémoire problématique
char* create_string(int size) {
    char* str = malloc(size);
    // Pas de vérification si malloc a réussi
    strcpy(str, "Default value");
    return str;
    // L'appelant doit libérer la mémoire mais ce n'est pas documenté
}
