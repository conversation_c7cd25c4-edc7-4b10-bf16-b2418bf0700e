/**
 * Utilitaires pour gérer différents types de fichiers
 */

export interface FileProcessingResult {
  content: string;
  originalName: string;
  fileType: string;
  success: boolean;
  error?: string;
  isFromOfficeFile?: boolean;
}

/**
 * Types de fichiers supportés
 */
export const SUPPORTED_FILE_TYPES = {
  // Fichiers texte
  TEXT: ['.txt', '.log', '.md', '.csv'],
  // Fichiers de code
  CODE: ['.c', '.cpp', '.ec', '.js', '.ts', '.py', '.java', '.php', '.html', '.css', '.sql'],
  // Fichiers de données
  DATA: ['.json', '.xml', '.yaml', '.yml'],
  // Fichiers Microsoft Office (nécessitent traitement spécial)
  OFFICE: ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'],
  // Fichiers PDF
  PDF: ['.pdf'],
  // Fichiers d'erreur spécifiques
  ERROR: ['.error', '.err', '.trace', '.dump']
};

/**
 * Obtient tous les types de fichiers supportés
 */
export function getAllSupportedTypes(): string[] {
  return [
    ...SUPPORTED_FILE_TYPES.TEXT,
    ...SUPPORTED_FILE_TYPES.CODE,
    ...SUPPORTED_FILE_TYPES.DATA,
    ...SUPPORTED_FILE_TYPES.OFFICE,
    ...SUPPORTED_FILE_TYPES.PDF,
    ...SUPPORTED_FILE_TYPES.ERROR
  ];
}

/**
 * Vérifie si un type de fichier est supporté
 */
export function isFileTypeSupported(fileName: string): boolean {
  const extension = getFileExtension(fileName);
  return getAllSupportedTypes().includes(extension);
}

/**
 * Obtient l'extension d'un fichier
 */
export function getFileExtension(fileName: string): string {
  const lastDot = fileName.lastIndexOf('.');
  return lastDot !== -1 ? fileName.substring(lastDot).toLowerCase() : '';
}

/**
 * Détermine le type de fichier
 */
export function getFileType(fileName: string): string {
  const extension = getFileExtension(fileName);
  
  if (SUPPORTED_FILE_TYPES.TEXT.includes(extension)) return 'text';
  if (SUPPORTED_FILE_TYPES.CODE.includes(extension)) return 'code';
  if (SUPPORTED_FILE_TYPES.DATA.includes(extension)) return 'data';
  if (SUPPORTED_FILE_TYPES.OFFICE.includes(extension)) return 'office';
  if (SUPPORTED_FILE_TYPES.PDF.includes(extension)) return 'pdf';
  if (SUPPORTED_FILE_TYPES.ERROR.includes(extension)) return 'error';
  
  return 'unknown';
}

/**
 * Traite un fichier selon son type
 */
export async function processFile(file: File): Promise<FileProcessingResult> {
  const fileType = getFileType(file.name);
  
  try {
    switch (fileType) {
      case 'text':
      case 'code':
      case 'data':
      case 'error':
        return await processTextFile(file);
      
      case 'office':
        return await processOfficeFile(file);
      
      case 'pdf':
        return await processPDFFile(file);
      
      default:
        // Essayer de lire comme fichier texte par défaut
        return await processTextFile(file);
    }
  } catch (error) {
    return {
      content: '',
      originalName: file.name,
      fileType,
      success: false,
      error: error instanceof Error ? error.message : 'Erreur inconnue'
    };
  }
}

/**
 * Traite les fichiers texte standard
 */
async function processTextFile(file: File): Promise<FileProcessingResult> {
  return new Promise((resolve) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      const content = e.target?.result as string;
      resolve({
        content,
        originalName: file.name,
        fileType: getFileType(file.name),
        success: true
      });
    };
    
    reader.onerror = () => {
      resolve({
        content: '',
        originalName: file.name,
        fileType: getFileType(file.name),
        success: false,
        error: 'Erreur lors de la lecture du fichier'
      });
    };
    
    reader.readAsText(file, 'UTF-8');
  });
}

/**
 * Traite les fichiers Office (Word, Excel, PowerPoint)
 * Utilise mammoth.js pour les fichiers Word (.docx)
 */
async function processOfficeFile(file: File): Promise<FileProcessingResult> {
  const extension = getFileExtension(file.name);

  try {
    // Traitement spécifique pour les fichiers Word (.docx)
    if (extension === '.docx') {
      return await processWordFile(file);
    }

    // Pour les autres fichiers Office (.doc, .xls, .xlsx, .ppt, .pptx)
    // On essaie une lecture basique avec nettoyage amélioré
    return await processOtherOfficeFile(file);

  } catch (error) {
    return {
      content: '',
      originalName: file.name,
      fileType: 'office',
      success: false,
      error: `Impossible de lire le fichier Office: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
    };
  }
}

/**
 * Traite spécifiquement les fichiers Word (.docx) avec mammoth
 */
async function processWordFile(file: File): Promise<FileProcessingResult> {
  try {
    // Import dynamique de mammoth pour éviter les erreurs si la bibliothèque n'est pas disponible
    const mammoth = await import('mammoth');

    const arrayBuffer = await file.arrayBuffer();
    const result = await mammoth.extractRawText({ arrayBuffer });

    if (result.value && result.value.trim().length > 0) {
      return {
        content: result.value.trim(),
        originalName: file.name,
        fileType: 'office',
        success: true,
        isFromOfficeFile: true
      };
    } else {
      return {
        content: '',
        originalName: file.name,
        fileType: 'office',
        success: false,
        error: 'Le fichier Word semble être vide ou corrompu',
        isFromOfficeFile: true
      };
    }
  } catch (error) {
    // Fallback si mammoth n'est pas disponible ou échoue
    console.warn('Mammoth non disponible ou erreur:', error);
    return await processOtherOfficeFile(file);
  }
}

/**
 * Traite les autres fichiers Office avec une approche de fallback
 */
async function processOtherOfficeFile(file: File): Promise<FileProcessingResult> {
  try {
    const arrayBuffer = await file.arrayBuffer();

    // Essayer de détecter si c'est un fichier XML (format Office moderne)
    const uint8Array = new Uint8Array(arrayBuffer);
    const isXMLBased = uint8Array[0] === 0x50 && uint8Array[1] === 0x4B; // ZIP signature (PK)

    if (isXMLBased) {
      // Pour les fichiers Office modernes (.docx, .xlsx, .pptx), on ne peut pas extraire facilement le texte
      // sans bibliothèques spécialisées
      return {
        content: '',
        originalName: file.name,
        fileType: 'office',
        success: false,
        error: `Fichier Office moderne détecté (${getFileExtension(file.name)}). Pour de meilleurs résultats, convertissez en .txt ou utilisez un fichier .docx pour Word.`,
        isFromOfficeFile: true
      };
    }

    // Pour les anciens formats Office (.doc, .xls, .ppt), essayer une lecture basique
    const decoder = new TextDecoder('utf-8', { fatal: false });
    const content = decoder.decode(arrayBuffer);

    // Nettoyer le contenu de manière plus agressive
    let cleanContent = content
      // Supprimer les caractères de contrôle et binaires
      .replace(/[\x00-\x1F\x7F-\x9F]/g, ' ')
      // Supprimer les séquences de caractères non-ASCII répétitives
      .replace(/[^\x20-\x7E\u00C0-\u017F\u0100-\u024F]{3,}/g, ' ')
      // Normaliser les espaces
      .replace(/\s+/g, ' ')
      .trim();

    // Filtrer pour garder seulement les parties qui semblent être du texte lisible
    const words = cleanContent.split(' ').filter(word => {
      // Garder les mots qui contiennent principalement des caractères lisibles
      return word.length > 2 && /^[a-zA-Z0-9\u00C0-\u017F\u0100-\u024F.,;:!?()-]+$/.test(word);
    });

    cleanContent = words.join(' ');

    if (cleanContent.length < 50) {
      return {
        content: '',
        originalName: file.name,
        fileType: 'office',
        success: false,
        error: 'Fichier Office traité avec support limité. Pour de meilleurs résultats, convertissez en .txt',
        isFromOfficeFile: true
      };
    }

    return {
      content: cleanContent,
      originalName: file.name,
      fileType: 'office',
      success: true,
      isFromOfficeFile: true
    };
  } catch (error) {
    return {
      content: '',
      originalName: file.name,
      fileType: 'office',
      success: false,
      error: 'Impossible de lire le fichier Office. Essayez de le convertir en .txt',
      isFromOfficeFile: true
    };
  }
}

/**
 * Traite les fichiers PDF
 * Note: Cette fonction nécessite pdf-parse ou une bibliothèque similaire pour un traitement complet
 */
async function processPDFFile(file: File): Promise<FileProcessingResult> {
  // Pour l'instant, on retourne une erreur explicative
  // Dans une implémentation complète, on utiliserait pdf-parse ou pdf.js
  
  return {
    content: '',
    originalName: file.name,
    fileType: 'pdf',
    success: false,
    error: 'Fichiers PDF non supportés actuellement. Convertissez en .txt pour l\'analyse'
  };
}

/**
 * Obtient une description des types de fichiers supportés
 */
export function getSupportedTypesDescription(): string {
  return `
Types de fichiers supportés :
• Texte : ${SUPPORTED_FILE_TYPES.TEXT.join(', ')}
• Code : ${SUPPORTED_FILE_TYPES.CODE.join(', ')}
• Données : ${SUPPORTED_FILE_TYPES.DATA.join(', ')}
• Erreurs : ${SUPPORTED_FILE_TYPES.ERROR.join(', ')}
• Office : ${SUPPORTED_FILE_TYPES.OFFICE.join(', ')} (support limité)
• PDF : ${SUPPORTED_FILE_TYPES.PDF.join(', ')} (non supporté actuellement)
  `.trim();
}
