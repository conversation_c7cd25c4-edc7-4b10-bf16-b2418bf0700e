import openai from '@/lib/openai';
import { ErrorAnalysisResult, ProgramFile, Agent, AutoFixSuggestion } from '@/types';
import { parseErrorFile, extractLineFromProgram, ParsedError, analyzeCodeContext } from '@/lib/utils';
import { estimateTokens, exceedsTokenLimit, splitTextIntoChunks, validatePromptTokens, optimizePromptForTokenLimit } from '@/lib/textUtils';

export const errorAnalysisAgent: Agent = {
  id: 'error-analysis-agent',
  name: 'Agent d\'Analyse d\'Erreurs',
  description: 'Analyse les fichiers de programme et d\'erreur pour détecter les erreurs, leurs emplacements et proposer des solutions',
  role: 'Expert en analyse d\'erreurs et débogage de programmes',
  goal: 'Identifier, localiser et proposer des solutions pour les erreurs dans les programmes',
  prompt: `Tu es un expert en analyse d'erreurs et débogage de programmes avec des capacités avancées d'analyse statique.
  Ton rôle est d'analyser les fichiers de programme et les fichiers d'erreur pour :
  1. Détecter et identifier les erreurs avec précision
  2. Localiser exactement où elles se produisent (ligne, colonne si possible)
  3. Analyser le contexte du code autour des erreurs
  4. Expliquer les causes possibles avec des détails techniques
  5. Proposer des solutions concrètes et détaillées
  6. Suggérer des corrections automatiques quand c'est possible
  7. Identifier les erreurs liées ou en cascade
  8. Proposer des améliorations préventives

  Utilise l'analyse contextuelle fournie (variables, fonctions, problèmes potentiels) pour enrichir tes diagnostics.
  Sois très précis dans tes analyses et fournis des solutions pratiques et applicables.
  Priorise les erreurs par ordre de sévérité et d'impact.`,
  tools: ['file-analysis', 'error-parsing', 'solution-generation'],
  utils: ['parseErrorFile', 'formatDate']
};

export class ErrorAnalysisService {
  private agent: Agent;

  constructor() {
    this.agent = errorAnalysisAgent;
  }

  async analyzeFiles(programFile: ProgramFile, errorFile: ProgramFile): Promise<ErrorAnalysisResult> {
    try {
      // Parse error file to extract structured error information
      const parsedErrors = parseErrorFile(errorFile.content);

      // Analyse statique préliminaire du code
      const staticAnalysis = this.performStaticAnalysis(programFile.content);

      // Limit program file content to avoid token limit but keep relevant sections
      let programContent = this.optimizeContentForAnalysis(programFile.content, parsedErrors);

      // Enrichir les erreurs parsées avec l'analyse contextuelle
      const enrichedErrors = this.enrichErrorsWithContext(parsedErrors, programFile.content);

      // Construire le prompt de manière optimisée pour éviter le dépassement de tokens
      const prompt = this.buildOptimizedPrompt(
        programFile,
        errorFile,
        programContent,
        staticAnalysis,
        enrichedErrors
      );

      // Validation finale avant envoi à l'API
      const validation = validatePromptTokens(prompt, 7500); // Marge de sécurité
      if (!validation.isValid) {
        console.warn(`Prompt dépasse la limite de tokens: ${validation.estimatedTokens} tokens (dépasse de ${validation.exceedsBy})`);
        console.warn(`Recommandation: ${validation.recommendation}`);

        // En cas de dépassement, utiliser une approche de fallback
        throw new Error(`Prompt trop volumineux: ${validation.estimatedTokens} tokens. ${validation.recommendation}`);
      }

      const response = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: this.agent.prompt
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 2000
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('Aucune réponse reçue de l\'API OpenAI');
      }

      // Try to parse JSON response
      let analysisResult: ErrorAnalysisResult;
      try {
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          analysisResult = JSON.parse(jsonMatch[0]);
        } else {
          // Fallback if no JSON found
          analysisResult = {
            summary: content,
            errors: [],
            recommendations: [],
            timestamp: new Date().toISOString()
          };
        }
      } catch (parseError) {
        // Fallback parsing
        analysisResult = {
          summary: content,
          errors: [],
          recommendations: [],
          timestamp: new Date().toISOString()
        };
      }

      analysisResult.timestamp = new Date().toISOString();

      // Enrichir les erreurs avec le contexte de code et les suggestions de correction
      if (analysisResult.errors) {
        analysisResult.errors = analysisResult.errors.map((error, index) => {
          let enrichedError = { ...error };

          if (error.lineNumber && error.lineNumber > 0) {
            // Ajouter le contexte de code
            const codeContext = extractLineFromProgram(programFile.content, error.lineNumber);
            enrichedError.codeContext = codeContext;

            // Générer des suggestions de correction automatique
            const parsedError = enrichedErrors.find(pe => pe.lineNumber === error.lineNumber);
            if (parsedError) {
              enrichedError.autoFixSuggestions = this.generateAutoFixSuggestions(parsedError, programFile.content);
            }
          }

          return enrichedError;
        });

        // Identifier les erreurs liées
        analysisResult.errors = this.identifyRelatedErrors(analysisResult.errors);
      }

      return analysisResult;

    } catch (error) {
      console.error('Erreur lors de l\'analyse:', error);
      throw new Error(`Erreur lors de l'analyse des fichiers: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
  }

  getAgentInfo(): Agent {
    return this.agent;
  }

  private performStaticAnalysis(programContent: string): {
    totalLines: number;
    functions: string[];
    variables: string[];
    potentialIssues: string[];
    complexity: 'LOW' | 'MEDIUM' | 'HIGH';
  } {
    const lines = programContent.split('\n');
    const totalLines = lines.length;

    // Analyse globale du code
    const functions = new Set<string>();
    const variables = new Set<string>();
    const potentialIssues: string[] = [];

    // Patterns pour détecter les fonctions
    const functionPatterns = [
      /(?:int|float|double|char|long|short|unsigned|signed|bool|void)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,
      /function\s+([a-zA-Z_][a-zA-Z0-9_]*)/g
    ];

    // Patterns pour détecter les variables
    const variablePatterns = [
      /(?:int|float|double|char|long|short|unsigned|signed|bool)\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,
      /(?:var|let|const)\s+([a-zA-Z_][a-zA-Z0-9_]*)/g
    ];

    // Analyse ligne par ligne
    lines.forEach((line, index) => {
      // Détecter les fonctions
      functionPatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          functions.add(match[1]);
        }
      });

      // Détecter les variables
      variablePatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          variables.add(match[1]);
        }
      });

      // Détecter les problèmes potentiels
      if (line.includes('malloc') && !line.includes('free')) {
        potentialIssues.push(`Ligne ${index + 1}: Allocation mémoire sans libération visible`);
      }
      if (line.includes('strcpy') || line.includes('strcat')) {
        potentialIssues.push(`Ligne ${index + 1}: Fonction potentiellement dangereuse détectée`);
      }
      if (line.includes('TODO') || line.includes('FIXME')) {
        potentialIssues.push(`Ligne ${index + 1}: Code incomplet ou problématique`);
      }
    });

    // Calculer la complexité
    let complexity: 'LOW' | 'MEDIUM' | 'HIGH' = 'LOW';
    if (totalLines > 1000 || functions.size > 50) {
      complexity = 'HIGH';
    } else if (totalLines > 500 || functions.size > 20) {
      complexity = 'MEDIUM';
    }

    return {
      totalLines,
      functions: Array.from(functions),
      variables: Array.from(variables),
      potentialIssues,
      complexity
    };
  }

  private optimizeContentForAnalysis(programContent: string, parsedErrors: ParsedError[]): string {
    const lines = programContent.split('\n');
    const totalLines = lines.length;

    // Estimation plus précise des tokens pour éviter le dépassement
    const estimatedTokens = estimateTokens(programContent);
    const maxAllowedTokens = 4000; // Marge de sécurité plus importante

    // Si le contenu est dans la limite de tokens, le garder entier
    if (estimatedTokens <= maxAllowedTokens) {
      return programContent;
    }

    // Identifier les lignes importantes basées sur les erreurs
    const importantLines = new Set<number>();
    parsedErrors.forEach(error => {
      if (error.lineNumber) {
        // Réduire le contexte autour des erreurs pour économiser des tokens
        const contextSize = Math.min(5, Math.floor(maxAllowedTokens / (parsedErrors.length * 20)));
        for (let i = Math.max(0, error.lineNumber - contextSize); i <= Math.min(totalLines - 1, error.lineNumber + contextSize); i++) {
          importantLines.add(i);
        }
      }
    });

    // Réduire le nombre de lignes du début et de la fin
    const headerFooterSize = Math.min(20, Math.floor(maxAllowedTokens / 100));
    for (let i = 0; i < Math.min(headerFooterSize, totalLines); i++) {
      importantLines.add(i);
    }
    for (let i = Math.max(0, totalLines - headerFooterSize); i < totalLines; i++) {
      importantLines.add(i);
    }

    // Construire le contenu optimisé
    const sortedLines = Array.from(importantLines).sort((a, b) => a - b);
    let optimizedContent = '';
    let lastLine = -1;

    sortedLines.forEach(lineIndex => {
      if (lineIndex > lastLine + 1) {
        optimizedContent += '\n... [LIGNES OMISES] ...\n';
      }
      optimizedContent += `${lineIndex + 1}: ${lines[lineIndex]}\n`;
      lastLine = lineIndex;
    });

    // Vérification finale et troncature si nécessaire
    if (estimateTokens(optimizedContent) > maxAllowedTokens) {
      const chunks = splitTextIntoChunks(optimizedContent, maxAllowedTokens * 4); // 4 chars per token
      optimizedContent = chunks[0] + '\n\n... [CONTENU TRONQUÉ POUR RESPECTER LA LIMITE DE TOKENS] ...';
    }

    return optimizedContent;
  }

  private buildOptimizedPrompt(
    programFile: ProgramFile,
    errorFile: ProgramFile,
    programContent: string,
    staticAnalysis: any,
    enrichedErrors: ParsedError[]
  ): string {
    // Estimation des tokens pour chaque section
    const basePrompt = this.agent.prompt;
    const baseTokens = estimateTokens(basePrompt);

    // Budget de tokens disponible (en gardant une marge pour la réponse)
    const maxInputTokens = 6000; // Marge de sécurité importante
    let remainingTokens = maxInputTokens - baseTokens;

    // Sections du prompt par ordre de priorité
    const sections: { content: string; priority: number; tokens: number }[] = [];

    // 1. Erreurs parsées (priorité maximale)
    const errorsContent = JSON.stringify(enrichedErrors, null, 2);
    sections.push({
      content: `ERREURS PARSÉES AVEC CONTEXTE:\n${errorsContent}`,
      priority: 1,
      tokens: estimateTokens(errorsContent)
    });

    // 2. Contenu du fichier d'erreur (priorité élevée)
    const errorFileContent = `FICHIER D'ERREUR:\nNom: ${errorFile.name}\nContenu:\n${errorFile.content}`;
    sections.push({
      content: errorFileContent,
      priority: 2,
      tokens: estimateTokens(errorFileContent)
    });

    // 3. Contenu du programme optimisé (priorité élevée)
    const programFileContent = `FICHIER PROGRAMME:\nNom: ${programFile.name}\nContenu (optimisé pour l'analyse):\n${programContent}`;
    sections.push({
      content: programFileContent,
      priority: 3,
      tokens: estimateTokens(programFileContent)
    });

    // 4. Analyse statique (priorité moyenne)
    const staticAnalysisContent = `ANALYSE STATIQUE DU PROGRAMME:\n${JSON.stringify(staticAnalysis, null, 2)}`;
    sections.push({
      content: staticAnalysisContent,
      priority: 4,
      tokens: estimateTokens(staticAnalysisContent)
    });

    // Instructions (priorité élevée mais compacte)
    const instructions = `
Analyse ces fichiers et fournis une réponse JSON structurée avec:
{
  "summary": "Résumé général de l'analyse avec priorités",
  "errors": [
    {
      "errorType": "Type d'erreur spécifique",
      "location": "Emplacement précis avec contexte",
      "description": "Description détaillée avec analyse technique",
      "possibleCauses": ["cause1", "cause2"],
      "solutions": ["solution1", "solution2"],
      "severity": "LOW|MEDIUM|HIGH|CRITICAL",
      "lineNumber": 123,
      "fileName": "nom_du_fichier.ext"
    }
  ],
  "recommendations": ["recommandation1", "recommandation2"],
  "preventiveMeasures": ["mesure1", "mesure2"]
}

INSTRUCTIONS:
- Priorise les erreurs par sévérité
- Propose des corrections automatiques
- Identifie les erreurs liées
- Sois précis sur les numéros de ligne`;

    // Trier les sections par priorité et inclure celles qui rentrent dans le budget
    sections.sort((a, b) => a.priority - b.priority);

    let finalPrompt = basePrompt + '\n\n';

    for (const section of sections) {
      if (section.tokens <= remainingTokens) {
        finalPrompt += section.content + '\n\n';
        remainingTokens -= section.tokens;
      } else {
        // Si la section ne rentre pas, essayer de la tronquer
        if (section.priority <= 3) { // Sections critiques
          const maxChars = remainingTokens * 4; // 4 chars per token approximation
          if (maxChars > 100) {
            const truncatedContent = section.content.substring(0, maxChars) + '\n... [CONTENU TRONQUÉ] ...';
            finalPrompt += truncatedContent + '\n\n';
            remainingTokens = 0;
            break;
          }
        }
      }
    }

    finalPrompt += instructions;

    return finalPrompt;
  }

  private optimizeContentForStaticAnalysis(programContent: string): string {
    const estimatedTokens = estimateTokens(programContent);
    const maxAllowedTokens = 3000; // Plus conservateur pour l'analyse statique

    if (estimatedTokens <= maxAllowedTokens) {
      return programContent;
    }

    // Pour l'analyse statique, on garde plus de contexte général
    const lines = programContent.split('\n');
    const totalLines = lines.length;

    // Garder le début (déclarations, imports)
    const headerSize = Math.min(100, Math.floor(totalLines * 0.2));
    // Garder la fin (fonctions principales)
    const footerSize = Math.min(100, Math.floor(totalLines * 0.2));
    // Échantillonner le milieu
    const middleSize = Math.min(200, totalLines - headerSize - footerSize);

    let optimizedLines: string[] = [];

    // Ajouter le début
    optimizedLines = optimizedLines.concat(lines.slice(0, headerSize));

    if (middleSize > 0) {
      optimizedLines.push('... [SECTION INTERMÉDIAIRE ÉCHANTILLONNÉE] ...');
      const middleStart = headerSize;
      const middleEnd = totalLines - footerSize;
      const step = Math.max(1, Math.floor((middleEnd - middleStart) / middleSize));

      for (let i = middleStart; i < middleEnd; i += step) {
        if (optimizedLines.length < maxAllowedTokens / 10) { // Estimation grossière
          optimizedLines.push(`${i + 1}: ${lines[i]}`);
        }
      }
    }

    // Ajouter la fin
    if (footerSize > 0) {
      optimizedLines.push('... [SECTION FINALE] ...');
      optimizedLines = optimizedLines.concat(
        lines.slice(-footerSize).map((line, index) =>
          `${totalLines - footerSize + index + 1}: ${line}`
        )
      );
    }

    return optimizedLines.join('\n');
  }

  private buildOptimizedStaticAnalysisPrompt(
    programFile: ProgramFile,
    optimizedContent: string,
    staticAnalysis: any,
    detectedIssues: any[]
  ): string {
    const basePrompt = this.agent.prompt;
    const maxInputTokens = 6000;
    let remainingTokens = maxInputTokens - estimateTokens(basePrompt);

    const sections: { content: string; priority: number; tokens: number }[] = [];

    // 1. Contenu du programme (priorité maximale)
    const programContent = `FICHIER PROGRAMME:\nNom: ${programFile.name}\nContenu (optimisé):\n${optimizedContent}`;
    sections.push({
      content: programContent,
      priority: 1,
      tokens: estimateTokens(programContent)
    });

    // 2. Problèmes détectés (priorité élevée)
    const issuesContent = `PROBLÈMES DÉTECTÉS:\n${JSON.stringify(detectedIssues, null, 2)}`;
    sections.push({
      content: issuesContent,
      priority: 2,
      tokens: estimateTokens(issuesContent)
    });

    // 3. Analyse statique (priorité moyenne)
    const analysisContent = `ANALYSE STATIQUE:\n${JSON.stringify(staticAnalysis, null, 2)}`;
    sections.push({
      content: analysisContent,
      priority: 3,
      tokens: estimateTokens(analysisContent)
    });

    const instructions = `
ANALYSE STATIQUE SEULE (sans fichier d'erreur):

Effectue une analyse statique complète et fournis une réponse JSON avec:
{
  "summary": "Résumé de l'analyse statique",
  "errors": [
    {
      "errorType": "Type de problème",
      "location": "Emplacement",
      "description": "Description",
      "possibleCauses": ["causes"],
      "solutions": ["solutions"],
      "severity": "LOW|MEDIUM|HIGH|CRITICAL",
      "lineNumber": 123
    }
  ],
  "recommendations": ["améliorations"],
  "codeQuality": {
    "score": 85,
    "strengths": ["points forts"],
    "weaknesses": ["points faibles"],
    "maintainability": "LOW|MEDIUM|HIGH"
  }
}`;

    // Construire le prompt final
    let finalPrompt = basePrompt + '\n\n';

    sections.sort((a, b) => a.priority - b.priority);

    for (const section of sections) {
      if (section.tokens <= remainingTokens) {
        finalPrompt += section.content + '\n\n';
        remainingTokens -= section.tokens;
      } else if (section.priority <= 2) {
        // Tronquer les sections critiques si nécessaire
        const maxChars = remainingTokens * 4;
        if (maxChars > 100) {
          const truncatedContent = section.content.substring(0, maxChars) + '\n... [TRONQUÉ] ...';
          finalPrompt += truncatedContent + '\n\n';
          remainingTokens = 0;
          break;
        }
      }
    }

    finalPrompt += instructions;
    return finalPrompt;
  }

  private enrichErrorsWithContext(parsedErrors: ParsedError[], programContent: string): ParsedError[] {
    return parsedErrors.map(error => {
      if (error.lineNumber) {
        const contextAnalysis = analyzeCodeContext(programContent.split('\n'), error.lineNumber);
        return {
          ...error,
          context: {
            variables: contextAnalysis.variables,
            functions: contextAnalysis.functions,
            potentialIssues: contextAnalysis.potentialIssues,
            suggestions: contextAnalysis.suggestions
          }
        };
      }
      return error;
    });
  }

  private generateAutoFixSuggestions(error: ParsedError, programContent: string): AutoFixSuggestion[] {
    const suggestions: AutoFixSuggestion[] = [];

    if (!error.lineNumber) return suggestions;

    const lines = programContent.split('\n');
    const targetLine = lines[error.lineNumber - 1];

    // Suggestions basées sur le type d'erreur
    switch (error.errorType) {
      case 'VARIABLE_NOT_INITIALIZED':
        if (targetLine.includes('=')) {
          suggestions.push({
            type: 'REPLACE',
            description: 'Initialiser la variable à une valeur par défaut',
            originalCode: targetLine,
            suggestedCode: targetLine.replace(/([a-zA-Z_][a-zA-Z0-9_]*)\s*;/, '$1 = 0;'),
            confidence: 'MEDIUM',
            lineNumber: error.lineNumber
          });
        }
        break;

      case 'RESOURCE_LOCKED':
        suggestions.push({
          type: 'INSERT',
          description: 'Ajouter une vérification de verrou avant exécution',
          suggestedCode: 'if (!is_task_running(task_name)) {',
          confidence: 'HIGH',
          lineNumber: error.lineNumber
        });
        break;

      case 'SQL_ERROR':
        if (targetLine.includes('EXEC SQL') && !targetLine.includes('SQLCODE_ERROR')) {
          suggestions.push({
            type: 'INSERT',
            description: 'Ajouter la vérification d\'erreur SQL',
            suggestedCode: 'SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "description");',
            confidence: 'HIGH',
            lineNumber: error.lineNumber + 1
          });
        }
        break;
    }

    return suggestions;
  }

  private identifyRelatedErrors(errors: any[]): any[] {
    return errors.map((error, index) => {
      const relatedErrors: number[] = [];

      // Chercher des erreurs sur des lignes proches
      errors.forEach((otherError, otherIndex) => {
        if (index !== otherIndex && error.lineNumber && otherError.lineNumber) {
          const lineDiff = Math.abs(error.lineNumber - otherError.lineNumber);
          if (lineDiff <= 5) {
            relatedErrors.push(otherIndex);
          }
        }
      });

      // Chercher des erreurs du même type
      errors.forEach((otherError, otherIndex) => {
        if (index !== otherIndex && error.errorType === otherError.errorType) {
          relatedErrors.push(otherIndex);
        }
      });

      return {
        ...error,
        relatedErrors: [...new Set(relatedErrors)] // Supprimer les doublons
      };
    });
  }

  // Nouvelle méthode pour analyser un programme sans fichier d'erreur
  async analyzeCodeOnly(programFile: ProgramFile): Promise<ErrorAnalysisResult> {
    try {
      // Analyse statique complète
      const staticAnalysis = this.performStaticAnalysis(programFile.content);
      const detectedIssues = this.detectStaticIssues(programFile.content);

      // Optimiser le contenu du programme pour éviter le dépassement de tokens
      const optimizedContent = this.optimizeContentForStaticAnalysis(programFile.content);

      const prompt = this.buildOptimizedStaticAnalysisPrompt(
        programFile,
        optimizedContent,
        staticAnalysis,
        detectedIssues
      );

      // Validation finale avant envoi à l'API
      const validation = validatePromptTokens(prompt, 7500);
      if (!validation.isValid) {
        console.warn(`Prompt d'analyse statique dépasse la limite: ${validation.estimatedTokens} tokens`);
        console.warn(`Recommandation: ${validation.recommendation}`);

        // En cas de dépassement, utiliser une approche de fallback plus agressive
        throw new Error(`Prompt trop volumineux pour l'analyse statique: ${validation.estimatedTokens} tokens. ${validation.recommendation}`);
      }

      const response = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: this.agent.prompt
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.2,
        max_tokens: 3000
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('Aucune réponse reçue de l\'API OpenAI');
      }

      // Parse JSON response
      let analysisResult: ErrorAnalysisResult;
      try {
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          analysisResult = JSON.parse(jsonMatch[0]);
        } else {
          analysisResult = {
            summary: content,
            errors: [],
            recommendations: [],
            timestamp: new Date().toISOString()
          };
        }
      } catch (parseError) {
        analysisResult = {
          summary: content,
          errors: [],
          recommendations: [],
          timestamp: new Date().toISOString()
        };
      }

      analysisResult.timestamp = new Date().toISOString();

      // Enrichir avec le contexte de code
      if (analysisResult.errors) {
        analysisResult.errors = analysisResult.errors.map(error => {
          if (error.lineNumber && error.lineNumber > 0) {
            const codeContext = extractLineFromProgram(programFile.content, error.lineNumber);
            return {
              ...error,
              codeContext
            };
          }
          return error;
        });
      }

      return analysisResult;

    } catch (error) {
      console.error('Erreur lors de l\'analyse statique:', error);
      throw new Error(`Erreur lors de l'analyse statique: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
  }

  private detectStaticIssues(programContent: string): Array<{
    type: string;
    description: string;
    lineNumber?: number;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  }> {
    const issues: Array<{
      type: string;
      description: string;
      lineNumber?: number;
      severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    }> = [];

    const lines = programContent.split('\n');

    lines.forEach((line, index) => {
      const lineNumber = index + 1;

      // Détection de problèmes de sécurité
      if (/strcpy|strcat|sprintf|gets/.test(line)) {
        issues.push({
          type: 'SECURITY_RISK',
          description: 'Utilisation de fonction potentiellement dangereuse',
          lineNumber,
          severity: 'HIGH'
        });
      }

      // Détection de fuites mémoire potentielles
      if (/malloc|calloc/.test(line) && !programContent.includes('free')) {
        issues.push({
          type: 'MEMORY_LEAK',
          description: 'Allocation mémoire sans libération visible',
          lineNumber,
          severity: 'MEDIUM'
        });
      }

      // Détection de variables non initialisées
      if (/(?:int|float|double|char)\s+[a-zA-Z_][a-zA-Z0-9_]*\s*;/.test(line)) {
        issues.push({
          type: 'UNINITIALIZED_VARIABLE',
          description: 'Variable déclarée mais non initialisée',
          lineNumber,
          severity: 'MEDIUM'
        });
      }

      // Détection de code mort
      if (/\/\*.*\*\//.test(line) && line.includes('TODO')) {
        issues.push({
          type: 'DEAD_CODE',
          description: 'Code commenté ou incomplet',
          lineNumber,
          severity: 'LOW'
        });
      }

      // Détection de complexité excessive
      if ((line.match(/if|while|for|switch/g) || []).length > 3) {
        issues.push({
          type: 'HIGH_COMPLEXITY',
          description: 'Ligne avec complexité cyclomatique élevée',
          lineNumber,
          severity: 'LOW'
        });
      }

      // Détection de SQL sans vérification d'erreur
      if (/EXEC SQL/.test(line) && !lines[index + 1]?.includes('SQLCODE_ERROR')) {
        issues.push({
          type: 'SQL_NO_ERROR_CHECK',
          description: 'Requête SQL sans vérification d\'erreur',
          lineNumber,
          severity: 'HIGH'
        });
      }
    });

    return issues;
  }
}
