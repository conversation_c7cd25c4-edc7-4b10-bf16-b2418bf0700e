<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test JavaScript</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn-green { background-color: #4CAF50; color: white; }
        .btn-blue { background-color: #2196F3; color: white; }
        .btn-red { background-color: #f44336; color: white; }
        .result {
            margin-top: 20px;
            padding: 10px;
            background-color: #e8f5e8;
            border-radius: 5px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test JavaScript Complet</h1>
        <p>Cette page teste si JavaScript fonctionne correctement.</p>
        
        <h2>Tests de Base :</h2>
        
        <button class="btn-green" onclick="testBasic()">
            Test 1: Alert Simple
        </button>
        
        <button class="btn-blue" onclick="testConsole()">
            Test 2: Console Log
        </button>
        
        <button class="btn-red" onclick="testDOM()">
            Test 3: Manipulation DOM
        </button>
        
        <div id="result" class="result">
            <h3>✅ Résultats des Tests :</h3>
            <ul id="results-list"></ul>
        </div>
        
        <h2>Instructions :</h2>
        <ol>
            <li>Cliquez sur chaque bouton dans l'ordre</li>
            <li>Ouvrez la console (F12 → Console)</li>
            <li>Notez quels tests fonctionnent</li>
        </ol>
    </div>

    <script>
        console.log('🚀 JavaScript chargé !');
        
        function testBasic() {
            console.log('Test 1: Alert appelé');
            alert('✅ Test 1 Réussi: JavaScript fonctionne !');
            addResult('Test 1: Alert - ✅ RÉUSSI');
        }
        
        function testConsole() {
            console.log('✅ Test 2 Réussi: Console.log fonctionne !');
            addResult('Test 2: Console - ✅ RÉUSSI (vérifiez la console)');
        }
        
        function testDOM() {
            console.log('Test 3: Manipulation DOM');
            const button = document.querySelector('.btn-red');
            button.style.backgroundColor = '#4CAF50';
            button.textContent = '✅ DOM Modifié !';
            addResult('Test 3: DOM - ✅ RÉUSSI (bouton changé)');
        }
        
        function addResult(text) {
            const resultDiv = document.getElementById('result');
            const resultsList = document.getElementById('results-list');
            
            const li = document.createElement('li');
            li.textContent = text;
            resultsList.appendChild(li);
            
            resultDiv.style.display = 'block';
        }
        
        // Test automatique au chargement
        window.onload = function() {
            console.log('✅ Page chargée, JavaScript opérationnel');
            addResult('Chargement - ✅ RÉUSSI');
        };
    </script>
</body>
</html>
