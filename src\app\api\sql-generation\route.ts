import { NextRequest, NextResponse } from 'next/server';
import { SQLGenerationService } from '@/agents/sqlGenerationAgent';
import { SQLGenerationRequest } from '@/types';
import { prepareSpecificationForAPI } from '@/lib/textUtils';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { specification, databaseType, includeComments, isFromOfficeFile }: SQLGenerationRequest = body;

    if (!specification || !specification.trim()) {
      return NextResponse.json(
        { error: 'La spécification est requise' },
        { status: 400 }
      );
    }

    console.log(`API SQL: Traitement ${isFromOfficeFile ? 'fichier Office' : 'texte normal'}`);
    console.log(`Longueur originale: ${specification.length} caractères`);

    const sqlGenerationService = new SQLGenerationService();
    const result = await sqlGenerationService.generateSQL({
      specification,
      databaseType: databaseType || 'mysql',
      includeComments: includeComments !== false,
      isFromOfficeFile: isFromOfficeFile || false
    });

    return NextResponse.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erreur lors de la génération SQL:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur interne du serveur',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  const sqlGenerationService = new SQLGenerationService();
  const agentInfo = sqlGenerationService.getAgentInfo();

  return NextResponse.json({
    success: true,
    data: agentInfo,
    timestamp: new Date().toISOString()
  });
}
