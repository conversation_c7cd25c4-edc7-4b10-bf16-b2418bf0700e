'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { SQLGenerationRequest, SQLGenerationResult } from '@/types';
import { Database, FileText, Clock, Copy, Download, Upload, AlertTriangle, Info, FileX } from 'lucide-react';
import { prepareSpecificationForAPI, estimateTokens, exceedsTokenLimit } from '@/lib/textUtils';
import { processFile, getAllSupportedTypes, getSupportedTypesDescription, isFileTypeSupported } from '@/lib/fileUtils';

export default function SQLGenerationPage() {
  const [specification, setSpecification] = useState('');
  const [databaseType, setDatabaseType] = useState<'mysql' | 'postgresql' | 'sqlite' | 'sqlserver'>('mysql');
  const [includeComments, setIncludeComments] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [result, setResult] = useState<SQLGenerationResult | null>(null);
  const [error, setError] = useState<string>('');
  const [textAnalysis, setTextAnalysis] = useState<{
    length: number;
    estimatedTokens: number;
    exceedsLimit: boolean;
    willBeTruncated: boolean;
  } | null>(null);
  const [uploadedFileName, setUploadedFileName] = useState<string>('');
  const [fileError, setFileError] = useState<string>('');
  const [isFromOfficeFile, setIsFromOfficeFile] = useState(false);

  // Analyser le texte pour estimer les tokens
  const analyzeText = (text: string) => {
    const length = text.length;
    const estimatedTokens = estimateTokens(text);
    const exceedsLimit = exceedsTokenLimit(text);
    const willBeTruncated = exceedsLimit;

    setTextAnalysis({
      length,
      estimatedTokens,
      exceedsLimit,
      willBeTruncated
    });
  };

  // Mettre à jour l'analyse quand le texte change
  const handleSpecificationChange = (value: string) => {
    setSpecification(value);
    analyzeText(value);
    // Si l'utilisateur tape manuellement, ce n'est plus un fichier Office
    if (!uploadedFileName) {
      setIsFromOfficeFile(false);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setFileError('');
    setUploadedFileName('');

    // Vérifier si le type de fichier est supporté
    if (!isFileTypeSupported(file.name)) {
      setFileError(`Type de fichier non supporté: ${file.name}`);
      return;
    }

    try {
      const result = await processFile(file);

      if (result.success) {
        setSpecification(result.content);
        setUploadedFileName(result.originalName);
        setIsFromOfficeFile(result.isFromOfficeFile || false);
        analyzeText(result.content);

        // Afficher un avertissement spécifique pour les fichiers Office
        if (result.fileType === 'office') {
          const extension = result.originalName.toLowerCase().split('.').pop();
          if (extension === 'docx') {
            setFileError('✅ Fichier Word (.docx) traité avec succès. Le contenu a été extrait automatiquement.');
          } else {
            setFileError(`⚠️ Fichier Office (${extension}) traité avec support limité. Pour de meilleurs résultats, convertissez en .txt ou utilisez un fichier .docx pour Word.`);
          }
        }
      } else {
        setFileError(result.error || 'Erreur lors du traitement du fichier');
      }
    } catch (error) {
      setFileError('Erreur lors du chargement du fichier');
    }
  };

  const handleGenerate = async () => {
    if (!specification.trim()) {
      setError('Veuillez fournir une spécification');
      return;
    }

    setIsGenerating(true);
    setError('');
    setResult(null);

    try {
      const request: SQLGenerationRequest = {
        specification,
        databaseType,
        includeComments,
        isFromOfficeFile
      };



      const response = await fetch('/api/sql-generation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erreur lors de la génération');
      }

      setResult(data.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Une erreur est survenue lors de la génération');
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const downloadSQL = () => {
    if (!result) return;
    
    const blob = new Blob([result.sql], { type: 'text/sql' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `generated_script_${new Date().toISOString().slice(0, 10)}.sql`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-[#002857] mb-4">
          Génération SQL
        </h1>
        <p className="text-gray-600">
          Générez des scripts SQL optimisés à partir de vos spécifications fonctionnelles.
        </p>
      </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Input Section avec design premium */}
          <div className="space-y-8">
            <Card className="bg-white/80 backdrop-blur-xl border-0 shadow-2xl shadow-[#002857]/10 rounded-3xl overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-[#002857] to-[#003d7a] text-white p-8">
                <CardTitle className="flex items-center text-2xl font-bold">
                  <FileText className="w-7 h-7 mr-3" />
                  Spécification
                </CardTitle>
                <CardDescription className="text-blue-100 text-base">
                  Décrivez les tables, relations et contraintes que vous souhaitez créer
                </CardDescription>
              </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex items-center gap-4 mb-4">
                  <Input
                    type="file"
                    accept={getAllSupportedTypes().join(',')}
                    onChange={handleFileUpload}
                    className="flex-1"
                  />
                  {uploadedFileName && (
                    <span className="text-sm text-green-600 flex items-center">
                      <Upload className="w-4 h-4 mr-1" />
                      {uploadedFileName}
                    </span>
                  )}
                  {fileError && (
                    <span className="text-sm text-red-600 flex items-center">
                      <FileX className="w-4 h-4 mr-1" />
                      {fileError}
                    </span>
                  )}
                </div>
                <div className="text-center text-gray-500 text-sm mb-4">
                  ou
                </div>

                {/* Information sur les types de fichiers supportés */}
                <details className="mb-4">
                  <summary className="text-sm text-gray-600 cursor-pointer hover:text-gray-800">
                    📁 Types de fichiers supportés
                  </summary>
                  <div className="mt-2 p-3 bg-gray-50 rounded text-xs text-gray-600">
                    <pre className="whitespace-pre-wrap">{getSupportedTypesDescription()}</pre>
                  </div>
                </details>
              </div>
              <Textarea
                placeholder="Exemple: Créer une base de données pour un système de gestion des commandes avec les tables suivantes:
- Table clients (id, nom, email, téléphone, adresse)
- Table produits (id, nom, description, prix, stock)
- Table commandes (id, client_id, date_commande, statut, total)
- Table détails_commande (id, commande_id, produit_id, quantité, prix_unitaire)

Contraintes:
- Clés étrangères appropriées
- Index sur les colonnes fréquemment recherchées
- Contraintes de validation sur les prix (> 0)"
                value={specification}
                onChange={(e) => handleSpecificationChange(e.target.value)}
                className="min-h-[300px]"
              />

              {/* Indicateur de longueur du texte */}
              {textAnalysis && (
                <div className={`mt-2 p-3 rounded-lg border ${
                  textAnalysis.exceedsLimit
                    ? 'bg-orange-50 border-orange-200'
                    : 'bg-blue-50 border-blue-200'
                }`}>
                  <div className="flex items-start gap-2">
                    {textAnalysis.exceedsLimit ? (
                      <AlertTriangle className="w-5 h-5 text-orange-500 mt-0.5" />
                    ) : (
                      <Info className="w-5 h-5 text-blue-500 mt-0.5" />
                    )}
                    <div className="flex-1">
                      <div className="text-sm font-medium">
                        {textAnalysis.exceedsLimit ? 'Texte très long détecté' : 'Analyse du texte'}
                      </div>
                      <div className="text-xs text-gray-600 mt-1">
                        <div>Longueur: {textAnalysis.length.toLocaleString()} caractères</div>
                        <div>Tokens estimés: {textAnalysis.estimatedTokens.toLocaleString()}</div>
                        {textAnalysis.willBeTruncated && (
                          <div className="text-orange-600 font-medium mt-1">
                            ⚠️ Le texte sera automatiquement résumé pour respecter les limites de l'IA
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-xl border-0 shadow-2xl shadow-[#ff7514]/10 rounded-3xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-[#ff7514] to-[#ff8c42] text-white p-8">
              <CardTitle className="flex items-center text-2xl font-bold">
                <Database className="w-7 h-7 mr-3" />
                Options de Génération
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Type de Base de Données</label>
                <select
                  value={databaseType}
                  onChange={(e) => setDatabaseType(e.target.value as 'mysql' | 'postgresql' | 'sqlite' | 'sqlserver')}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="mysql">MySQL</option>
                  <option value="postgresql">PostgreSQL</option>
                  <option value="sqlite">SQLite</option>
                  <option value="sqlserver">SQL Server</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="includeComments"
                  checked={includeComments}
                  onChange={(e) => setIncludeComments(e.target.checked)}
                  className="rounded"
                />
                <label htmlFor="includeComments" className="text-sm">
                  Inclure des commentaires explicatifs
                </label>
              </div>
            </CardContent>
          </Card>

          <Button
            onClick={handleGenerate}
            disabled={isGenerating || !specification.trim()}
            className="w-full bg-gradient-to-r from-[#002857] to-[#003d7a] hover:from-[#001a3d] hover:to-[#002857] text-white py-4 rounded-2xl shadow-xl shadow-[#002857]/25 transform hover:scale-105 transition-all duration-300"
            size="lg"
          >
            {isGenerating ? (
              <>
                <Clock className="w-5 h-5 mr-2 animate-spin" />
                Génération en cours...
              </>
            ) : (
              <>
                <Database className="w-5 h-5 mr-2" />
                Générer le Script SQL
              </>
            )}
          </Button>

          {error && (
            <div className="bg-[#002857]/5 border border-[#002857]/20 rounded-lg p-4">
              <p className="text-[#002857]">{error}</p>
            </div>
          )}
        </div>

        {/* Results Section avec design moderne */}
        <div>
          {result && (
            <div className="space-y-6">
              <Card className="bg-white/80 backdrop-blur-xl border-0 shadow-2xl shadow-[#ff7514]/10 rounded-3xl overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-[#ff7514] to-[#ff8c42] text-white p-8">
                  <CardTitle className="flex items-center justify-between text-2xl font-bold">
                    <span className="flex items-center">
                      <Database className="w-7 h-7 mr-3" />
                      Script SQL Généré
                    </span>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(result.sql)}
                        className="bg-white/20 border-white/30 text-white hover:bg-white/30"
                      >
                        <Copy className="w-4 h-4 mr-1" />
                        Copier
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={downloadSQL}
                        className="bg-white/20 border-white/30 text-white hover:bg-white/30"
                      >
                        <Download className="w-4 h-4 mr-1" />
                        Télécharger
                      </Button>
                    </div>
                  </CardTitle>
                  <CardDescription>
                    Généré le {new Date(result.timestamp).toLocaleString('fr-FR')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <pre className="bg-gray-50 p-4 rounded-lg overflow-x-auto text-sm font-mono whitespace-pre-wrap">
                    {result.sql}
                  </pre>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Explication</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 whitespace-pre-wrap">{result.explanation}</p>
                </CardContent>
              </Card>

              {(result.tables.length > 0 || result.operations.length > 0) && (
                <Card>
                  <CardHeader>
                    <CardTitle>Détails du Script</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {result.tables.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-2">Tables créées ({result.tables.length})</h4>
                        <div className="flex flex-wrap gap-2">
                          {result.tables.map((table, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm"
                            >
                              {table}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {result.operations.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-2">Opérations SQL</h4>
                        <div className="flex flex-wrap gap-2">
                          {result.operations.map((operation, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 bg-green-100 text-green-800 rounded text-sm"
                            >
                              {operation}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {!result && !isGenerating && (
            <Card>
              <CardContent className="text-center py-12">
                <Database className="w-16 h-16 mx-auto text-gray-300 mb-4" />
                <p className="text-gray-500">
                  Le script SQL généré apparaîtra ici une fois que vous aurez fourni une spécification et lancé la génération.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
