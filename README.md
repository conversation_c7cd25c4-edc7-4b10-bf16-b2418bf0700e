# Système d'Agents Intelligents Leoni

Un système moderne d'agents IA spécialisés pour l'analyse d'erreurs et la génération de scripts SQL, développé avec Next.js et intégrant OpenAI GPT-4.1.

## 🚀 Fonctionnalités

### Agent d'Analyse d'Erreurs
- **Analyse automatique** des fichiers de programme et d'erreur
- **Localisation précise** des problèmes dans le code
- **Solutions détaillées** et recommandations d'experts
- **Support multi-formats** (C, C++, EC, logs, etc.)
- **Interface intuitive** pour le téléchargement de fichiers

### Agent de Génération SQL
- **Génération automatique** de scripts SQL à partir de spécifications
- **Support multi-bases de données** (MySQL, PostgreSQL, SQLite, SQL Server)
- **Optimisation automatique** et bonnes pratiques
- **Documentation intégrée** avec commentaires explicatifs
- **Export et téléchargement** des scripts générés

## 🛠️ Technologies Utilisées

- **Frontend**: Next.js 15, React, TypeScript
- **Styling**: Tailwind CSS, Radix UI
- **IA**: OpenAI GPT-4.1
- **Icons**: Lucide React
- **Architecture**: API Routes, Services modulaires

## 📦 Installation

1. **Cloner le repository**
```bash
git clone <repository-url>
cd leoni-agents
```

2. **Installer les dépendances**
```bash
npm install
```

3. **Configurer les variables d'environnement**
Créer un fichier `.env.local` :
```env
OPENAI_API_KEY=votre_clé_api_openai
```

4. **Démarrer le serveur de développement**
```bash
npm run dev
```

5. **Ouvrir l'application**
Naviguer vers [http://localhost:3000](http://localhost:3000)

## 🏗️ Structure du Projet

```
leoni-agents/
├── src/
│   ├── agents/                 # Services des agents IA
│   │   ├── errorAnalysisAgent.ts
│   │   └── sqlGenerationAgent.ts
│   ├── app/                    # Pages et API routes
│   │   ├── api/               # API endpoints
│   │   ├── error-analysis/    # Page d'analyse d'erreurs
│   │   ├── sql-generation/    # Page de génération SQL
│   │   └── page.tsx          # Page d'accueil
│   ├── components/            # Composants React
│   │   ├── ui/               # Composants UI de base
│   │   └── Navigation.tsx    # Navigation principale
│   ├── lib/                  # Utilitaires et configuration
│   │   ├── openai.ts        # Configuration OpenAI
│   │   └── utils.ts         # Fonctions utilitaires
│   └── types/               # Types TypeScript
├── public/
│   └── examples/           # Fichiers d'exemple
└── README.md
```

## 🎯 Utilisation

### Agent d'Analyse d'Erreurs

1. **Naviguer** vers la page "Analyse d'Erreurs"
2. **Télécharger** ou coller le contenu du fichier programme
3. **Télécharger** ou coller le contenu du fichier d'erreur
4. **Cliquer** sur "Analyser les Erreurs"
5. **Consulter** les résultats détaillés avec solutions

### Agent de Génération SQL

1. **Naviguer** vers la page "Génération SQL"
2. **Saisir** la spécification fonctionnelle
3. **Choisir** le type de base de données
4. **Configurer** les options (commentaires, etc.)
5. **Cliquer** sur "Générer le Script SQL"
6. **Copier** ou télécharger le script généré

## 📋 Exemples

### Fichiers d'Exemple Inclus
- `caofors_program.txt` - Exemple de fichier programme C
- `caofors_error.txt` - Exemple de fichier d'erreur

### Exemple de Spécification SQL
```
Créer une base de données pour un système de gestion des commandes avec :
- Table clients (id, nom, email, téléphone, adresse)
- Table produits (id, nom, description, prix, stock)
- Table commandes (id, client_id, date_commande, statut, total)

Contraintes :
- Clés étrangères appropriées
- Index sur les colonnes fréquemment recherchées
- Contraintes de validation sur les prix (> 0)
```

## 🔧 Configuration

### Variables d'Environnement
- `OPENAI_API_KEY` - Clé API OpenAI (obligatoire)

### Personnalisation des Agents
Les agents peuvent être personnalisés en modifiant :
- **Prompts** : Dans les fichiers `src/agents/`
- **Modèles** : Configuration OpenAI dans `src/lib/openai.ts`
- **Types** : Interfaces TypeScript dans `src/types/`

## 🚀 Déploiement

### Vercel (Recommandé)
```bash
npm run build
vercel --prod
```

### Docker
```bash
docker build -t leoni-agents .
docker run -p 3000:3000 leoni-agents
```

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/nouvelle-fonctionnalite`)
3. Commit les changements (`git commit -am 'Ajouter nouvelle fonctionnalité'`)
4. Push vers la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. Créer une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🆘 Support

Pour toute question ou problème :
- Créer une issue sur GitHub
- Contacter l'équipe de développement Leoni

---

**Développé avec ❤️ par l'équipe Leoni**
